<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stripe Auto Fill - 设置</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.8;
        }
        
        .section {
            margin-bottom: 30px;
        }
        
        .section h2 {
            font-size: 20px;
            margin-bottom: 15px;
            color: #50e3c2;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            opacity: 0.9;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 14px;
            backdrop-filter: blur(10px);
        }
        
        .form-group input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #50e3c2;
            box-shadow: 0 0 0 2px rgba(80, 227, 194, 0.3);
        }
        
        .form-row {
            display: flex;
            gap: 15px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-right: 10px;
        }
        
        .btn.primary {
            background: #50e3c2;
            color: #000;
        }
        
        .btn.primary:hover {
            background: #45d4b3;
            transform: translateY(-1px);
        }
        
        .btn.secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }
        
        .btn.secondary:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .current-info {
            background: rgba(80, 227, 194, 0.2);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .current-info h3 {
            margin: 0 0 15px 0;
            color: #50e3c2;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .info-label {
            opacity: 0.8;
        }
        
        .info-value {
            font-weight: 500;
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            text-align: center;
            font-weight: 500;
        }
        
        .status.success {
            background: rgba(80, 227, 194, 0.3);
        }
        
        .status.error {
            background: rgba(255, 107, 107, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Stripe Auto Fill 设置</h1>
            <p>配置您的信用卡和地址信息</p>
        </div>
        
        <div class="section">
            <h2>当前信息</h2>
            <div class="current-info">
                <h3>预设卡信息</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">卡号:</span>
                        <span class="info-value">4937 **** **** 3546</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">有效期:</span>
                        <span class="info-value">06/28</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">CVC:</span>
                        <span class="info-value">124</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">姓名:</span>
                        <span class="info-value">Canestro</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">国家:</span>
                        <span class="info-value">Canada</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">地址:</span>
                        <span class="info-value">2225 137th Avenue</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">城市:</span>
                        <span class="info-value">Edmonton</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">州/省:</span>
                        <span class="info-value">Alberta</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">邮编:</span>
                        <span class="info-value">T5J 3P4</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>使用说明</h2>
            <div style="background: rgba(255, 255, 255, 0.1); padding: 20px; border-radius: 10px;">
                <ol style="margin: 0; padding-left: 20px;">
                    <li>导航到 Stripe 支付页面 (checkout.stripe.com 或 buy.stripe.com)</li>
                    <li>点击浏览器工具栏中的插件图标</li>
                    <li>点击"自动填写表单"按钮</li>
                    <li>如需要，点击"自动提交"按钮</li>
                    <li>插件会自动检测验证码并在完成后重新提交</li>
                </ol>
            </div>
        </div>
        
        <div class="section">
            <h2>功能特性</h2>
            <div style="background: rgba(255, 255, 255, 0.1); padding: 20px; border-radius: 10px;">
                <ul style="margin: 0; padding-left: 20px;">
                    <li>✅ 自动填写信用卡信息</li>
                    <li>✅ 自动填写账单地址</li>
                    <li>✅ 智能验证码检测</li>
                    <li>✅ 自动重新提交</li>
                    <li>✅ 支持多个 Stripe 域名</li>
                </ul>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <button class="btn secondary" onclick="window.close()">关闭设置</button>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
    </div>
</body>
</html>

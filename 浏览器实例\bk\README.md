# Stripe Auto Fill Chrome 插件

这是一个专门为 Stripe 支付页面设计的自动填写信用卡信息的 Chrome 浏览器插件。

## 功能特性

- ✅ 自动填写预设的信用卡信息
- ✅ 自动填写账单地址信息
- ✅ 智能验证码检测和自动重新提交
- ✅ 支持多个 Stripe 域名
- ✅ 简洁的用户界面
- ✅ 实时状态反馈

## 支持的网站

- `https://checkout.stripe.com/*`
- `https://buy.stripe.com/*`
- `*://*/c/pay/*`

## 预设信息

插件使用以下预设的卡信息：

- **卡号**: 4937 2420 1027 3546
- **有效期**: 06/28
- **CVC**: 124
- **姓名**: Canestro
- **国家**: Canada (CA)
- **地址**: 2225 137th Avenue
- **城市**: Edmonton
- **州/省**: Alberta
- **邮编**: T5J 3P4

## 安装方法

### 方法一：开发者模式安装

1. 打开 Chrome 浏览器
2. 在地址栏输入 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择包含插件文件的文件夹
6. 插件安装完成

### 方法二：打包安装

1. 在 `chrome://extensions/` 页面点击"打包扩展程序"
2. 选择插件文件夹，生成 `.crx` 文件
3. 将 `.crx` 文件拖拽到扩展程序页面进行安装

## 使用方法

1. **导航到支付页面**
   - 打开任何支持的 Stripe 支付页面

2. **使用插件**
   - 点击浏览器工具栏中的插件图标
   - 点击"自动填写表单"按钮
   - 如需要，点击"自动提交"按钮

3. **验证码处理**
   - 插件会自动检测验证码
   - 手动完成验证码后，插件会自动重新提交表单

## 文件结构

```
├── manifest.json          # 插件配置文件
├── content.js             # 内容脚本（主要逻辑）
├── popup.html             # 弹出窗口界面
├── popup.js               # 弹出窗口脚本
├── options.html           # 设置页面
├── styles.css             # 样式文件
├── README.md              # 说明文档
└── icons/                 # 图标文件夹（需要添加）
    ├── icon16.png
    ├── icon48.png
    └── icon128.png
```

## 图标文件

由于无法直接生成图片文件，您需要自行添加以下尺寸的图标：

- `icon16.png` - 16x16 像素
- `icon48.png` - 48x48 像素  
- `icon128.png` - 128x128 像素

建议使用简洁的信用卡或支付相关的图标设计。

## 注意事项

1. **安全性**: 此插件仅在本地运行，不会向外部服务器发送任何数据
2. **兼容性**: 专门为 Stripe 支付页面优化，可能不适用于其他支付平台
3. **更新**: 如果 Stripe 更改了页面结构，可能需要更新插件代码
4. **测试**: 建议先在测试环境中使用，确认功能正常后再用于生产环境

## 故障排除

### 插件无法加载
- 确认所有文件都在同一文件夹中
- 检查 `manifest.json` 文件格式是否正确
- 确认开启了开发者模式

### 自动填写不工作
- 确认当前页面是支持的 Stripe 域名
- 打开开发者工具查看控制台错误信息
- 尝试刷新页面后重新使用

### 验证码检测失败
- 手动完成验证码后等待几秒
- 如果仍未自动提交，可手动点击提交按钮

## 技术实现

- **Manifest V3**: 使用最新的 Chrome 扩展 API
- **Content Scripts**: 直接操作页面 DOM 元素
- **Message Passing**: Popup 与 Content Script 之间的通信
- **MutationObserver**: 监听页面变化以检测验证码

## 许可证

MIT License - 仅供学习和测试使用

## 免责声明

此插件仅供学习和测试目的使用。使用者需要自行承担使用风险，开发者不对任何损失负责。请确保在合法合规的前提下使用此插件。
